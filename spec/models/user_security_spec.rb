# ABOUTME: Security tests for User authorization cache invalidation
# ABOUTME: Tests TYM-55 fix to ensure fresh authorization data after role changes

require 'rails_helper'

RSpec.describe User, type: :model do
  describe 'TYM-55 Security: Authorization cache invalidation' do
    let(:user) { create(:user) }
    let(:company) { create(:company) }
    let(:owner_role) { create(:role, name: 'owner') }

    describe '#invalidate_role_cache!' do
      it 'clears the role cache instance variable' do
        # Pre-populate the cache
        user.instance_variable_set(:@company_roles, { company.id => owner_role })
        expect(user.instance_variable_get(:@company_roles)).to be_present
        
        # Call the invalidation method
        user.invalidate_role_cache!
        
        # Verify cache is cleared
        expect(user.instance_variable_get(:@company_roles)).to be_nil
      end

      it 'logs the cache invalidation for security audit' do
        expect(Rails.logger).to receive(:debug).with(/TYM-55 Security: Role cache invalidated for user #{user.id}/)
        
        user.invalidate_role_cache!
      end
    end

    describe 'authorization cache behavior' do
      before do
        create(:company_user_role, user: user, company: company, role: owner_role, active: true)
      end

      it 'uses cached role data on subsequent calls' do
        # First call should query database
        first_role = user.role_in(company)
        
        # Verify cache is populated
        expect(user.instance_variable_get(:@company_roles)).to include(company.id)
        
        # Second call should use cache (same object)
        second_role = user.role_in(company)
        expect(second_role).to be(first_role)
      end

      it 'refreshes role data after cache invalidation' do
        # Populate cache with initial role
        initial_role = user.role_in(company)
        expect(initial_role.name).to eq('owner')
        
        # Simulate role change in database (bypassing cache)
        employee_role = create(:role, name: 'employee')
        user.company_user_roles.find_by(company: company).update_column(:role_id, employee_role.id)
        
        # Cache still returns old data
        cached_role = user.role_in(company)
        expect(cached_role.name).to eq('owner')
        
        # After invalidation, fresh data is retrieved
        user.invalidate_role_cache!
        fresh_role = user.role_in(company)
        expect(fresh_role.name).to eq('employee')
      end
    end
  end
end