# ABOUTME: Integration security tests for company settings authorization bypass
# ABOUTME: Tests TYM-55 fix to prevent unauthorized company access after role changes

require 'rails_helper'

RSpec.describe 'Company Settings Security', type: :request do
  describe 'TYM-55 Security: Prevent unauthorized company settings access' do
    let(:company1) { create(:company) }
    let(:company2) { create(:company) }
    let(:user) { create(:user) }
    let(:owner_role) { create(:role, name: 'owner') }
    let(:employee_role) { create(:role, name: 'employee') }

    describe 'Scenario 1: Stale session after role deactivation' do
      before do
        # User starts as owner in company1
        @company_user_role = create(:company_user_role, 
          user: user, 
          company: company1, 
          role: owner_role, 
          active: true
        )
      end

      it 'denies access after role deactivation' do
        # Simulate JWT authentication with company1 context
        jwt_payload = {
          'user_id' => user.id,
          'company_id' => company1.id,
          'exp' => 1.hour.from_now.to_i
        }
        token = JwtService.encode(jwt_payload)

        # Initial request should succeed (user is owner)
        get "/api/v1/companies", 
            headers: { 'Authorization' => "Bearer #{token}" }
        expect(response).to have_http_status(:ok)

        # Deactivate user's role
        @company_user_role.update!(active: false)

        # Subsequent request with same JWT should fail
        get "/api/v1/companies", 
            headers: { 'Authorization' => "Bearer #{token}" }
        expect(response).to have_http_status(:unauthorized)
      end
    end

    describe 'Scenario 2: Cross-company access attempt' do
      before do
        # User has role in company1 but not company2
        create(:company_user_role, 
          user: user, 
          company: company1, 
          role: owner_role, 
          active: true
        )
      end

      it 'denies access to company2 settings with company2 JWT' do
        # Simulate compromised/manipulated JWT with wrong company_id
        jwt_payload = {
          'user_id' => user.id,
          'company_id' => company2.id,  # User doesn't have role in company2
          'exp' => 1.hour.from_now.to_i
        }
        token = JwtService.encode(jwt_payload)

        # Request should fail - user has no role in company2
        get "/api/v1/companies", 
            headers: { 'Authorization' => "Bearer #{token}" }
        expect(response).to have_http_status(:unauthorized)
      end
    end

    describe 'Scenario 3: Role downgrade during session' do
      before do
        @company_user_role = create(:company_user_role, 
          user: user, 
          company: company1, 
          role: owner_role, 
          active: true
        )
      end

      it 'enforces authorization after role change' do
        jwt_payload = {
          'user_id' => user.id,
          'company_id' => company1.id,
          'exp' => 1.hour.from_now.to_i
        }
        token = JwtService.encode(jwt_payload)

        # Initial request succeeds (user is owner)
        get "/api/v1/companies", 
            headers: { 'Authorization' => "Bearer #{token}" }
        expect(response).to have_http_status(:ok)

        # Change role from owner to employee
        @company_user_role.update!(role: employee_role)

        # Note: In real JWT invalidation, the token would be revoked
        # For this test, we verify that the authorization check uses real-time data
        # The JWT validation should still pass, but policy authorization might fail
        # depending on the specific endpoint's authorization requirements
        
        # Company viewing might still be allowed for employees
        get "/api/v1/companies", 
            headers: { 'Authorization' => "Bearer #{token}" }
        
        # The response depends on the specific policy - we mainly verify
        # that authorization uses fresh role data, not cached data
        expect([200, 403]).to include(response.status)
      end
    end

    describe 'JWT validation security' do
      it 'validates company access in real-time' do
        # Create initial role
        company_user_role = create(:company_user_role, 
          user: user, 
          company: company1, 
          role: owner_role, 
          active: true
        )

        jwt_payload = {
          'user_id' => user.id,
          'company_id' => company1.id,
          'exp' => 1.hour.from_now.to_i
        }
        token = JwtService.encode(jwt_payload)

        # Deactivate role after JWT creation
        company_user_role.update!(active: false)

        # JWT validation should fail due to real-time role check
        get "/api/v1/companies", 
            headers: { 'Authorization' => "Bearer #{token}" }
        expect(response).to have_http_status(:unauthorized)
      end

      it 'logs security events for unauthorized access attempts' do
        jwt_payload = {
          'user_id' => user.id,
          'company_id' => company1.id,  # User has no role in company1
          'exp' => 1.hour.from_now.to_i
        }
        token = JwtService.encode(jwt_payload)

        expect(AuthHealthCheck).to receive(:log_security_event)
          .with('unauthorized_company_access', hash_including(
            severity: 'high',
            user_id: user.id,
            company_id: company1.id,
            reason: 'no_active_role'
          ))

        get "/api/v1/companies", 
            headers: { 'Authorization' => "Bearer #{token}" }
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
end