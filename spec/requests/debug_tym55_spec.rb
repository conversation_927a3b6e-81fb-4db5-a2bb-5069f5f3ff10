# ABOUTME: Debug test to reproduce exact TYM-55 scenario from logs
# ABOUTME: Understanding how user=6 gets owner access to company=6 when they shouldn't

require 'rails_helper'

RSpec.describe 'TYM-55 Debug: Reproduce Exact Log Scenario', type: :request do
  let(:company_a) { create(:company, name: "Company A") }
  let(:company_b) { create(:company, name: "Company B") }  
  let(:user) { create(:user) }
  let(:owner_role) { create(:role, name: 'owner') }

  describe 'Reproducing log scenario: user=6, company=6, role=owner' do
    before do
      # User is only owner in Company A (ID=1) 
      create(:company_user_role, 
        user: user, 
        company: company_a, 
        role: owner_role, 
        active: true,
        is_primary: true
      )

      # Verify user has NO role in Company B (ID=6)
      expect(user.company_user_roles.where(company: company_b)).to be_empty
      
      # Ensure Company B exists and has settings
      company_b.create_company_setting! if company_b.company_setting.nil?
    end

    it 'reproduces the authorization bypass from logs' do
      # This JWT payload matches what would be in the logs:
      # "JWT tenant context set: company_id=6 for user=6 with role=owner"
      jwt_payload = {
        'user_id' => user.id,        # From logs: user=6  
        'company_id' => company_b.id,     # From logs: company_id=6
        'exp' => 1.hour.from_now.to_i
      }
      
      # Create the exact JWT from logs scenario
      token = JwtService.encode(jwt_payload)
      
      Rails.logger.info "DEBUG: Testing with JWT payload: #{jwt_payload}"
      
      # Make the exact request from logs
      put "/company_settings", 
          params: { company_setting: { approve_vacations: false } }.to_json,
          headers: { 
            'Authorization' => "Bearer #{token}",
            'Content-Type' => 'application/json'
          }
      
      Rails.logger.info "DEBUG: Response status: #{response.status}"
      Rails.logger.info "DEBUG: Response body: #{response.body}"
      
      # According to logs, this returned 200 OK when it should be 401/403
      # If JWT validation works correctly, this should fail
      expect(response).to have_http_status(:unauthorized), 
        "User with no role in company 6 should not be able to edit its settings"
    end

    it 'shows the role validation process step by step' do
      jwt_payload = {
        'user_id' => user.id,
        'company_id' => company_b.id, 
        'exp' => 1.hour.from_now.to_i
      }
      token = JwtService.encode(jwt_payload)
      
      # Debug: Check what the JWT validation should find
      Rails.logger.info "DEBUG: User roles in Company A: #{user.company_user_roles.where(company: company_a).pluck(:role_id, :active)}"
      Rails.logger.info "DEBUG: User roles in Company B: #{user.company_user_roles.where(company: company_b).pluck(:role_id, :active)}"
      
      # This should show that user has NO active role in company 6
      user_role_in_company_b = user.company_user_roles.active.find_by(company: company_b)
      expect(user_role_in_company_b).to be_nil, "User should have no role in Company B"
      
      # Make the request - JWT authentication should reject this
      put "/company_settings",
          params: { company_setting: { approve_vacations: false } }.to_json,
          headers: { 
            'Authorization' => "Bearer #{token}",
            'Content-Type' => 'application/json'
          }
      
      # This should fail at JWT authentication level
      expect(response).to have_http_status(:unauthorized)
    end
  end

  describe 'Understanding the current JWT validation' do
    it 'tests the JWT validation directly' do
      # Test what happens when we call the JWT validation manually
      jwt_payload = {
        'user_id' => user.id,
        'company_id' => company_b.id,
        'exp' => 1.hour.from_now.to_i
      }
      
      # Decode and validate manually
      decoded_payload = JwtService.decode(JwtService.encode(jwt_payload))
      
      expect(decoded_payload['user_id']).to eq(user.id)
      expect(decoded_payload['company_id']).to eq(company_b.id)
      
      # Check if user exists
      expect(User.find(user.id)).to eq(user)
      
      # Check if company exists  
      expect(Company.find(company_b.id)).to eq(company_b)
      
      # Check if user has role in company - THIS SHOULD BE FALSE
      user_company_role = user.company_user_roles.active.find_by(company: company_b)
      expect(user_company_role).to be_nil, "User should not have active role in Company B"
    end
  end
end