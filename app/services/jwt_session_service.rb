# app/services/jwt_session_service.rb
# Service for managing JWT sessions in Redis
# Stores JWT tokens server-side to maintain authentication across page refreshes
class JwtSessionService
  # Use centralized Redis namespacing to prevent key collisions
  SESSION_TTL = 90.days.to_i # Match refresh token expiry for "forever login" UX
  
  class << self
    # Create a new JWT session in Redis
    # @param user [User] The authenticated user
    # @param jwt_payload [Hash] Contains access_token, refresh_token, company_id
    # @return [String] The generated session_id
    def create_session(user, jwt_payload)
      session_id = SecureRandom.uuid
      
      session_data = {
        user_id: user.id,
        access_token: jwt_payload[:access_token],
        refresh_token: jwt_payload[:refresh_token],
        company_id: jwt_payload[:company_id],
        created_at: Time.current.to_i,
        last_activity: Time.current.to_i,
        ip_address: jwt_payload[:ip_address],
        user_agent: jwt_payload[:user_agent]
      }
      
      # Store session in Redis with TTL using namespaced service
      RedisService.set(:jwt_sessions, [user.id, session_id], session_data, ttl: SESSION_TTL)
      
      # Add session to user's session list for tracking
      add_to_user_sessions(user.id, session_id)
      
      Rails.logger.info "Created JWT session for user #{user.id}: #{session_id}"
      session_id
    rescue => e
      Rails.logger.error "Failed to create JWT session: #{e.message}"
      raise e
    end
    
    # Find a session by user_id and session_id
    # @param user_id [Integer] The user's ID
    # @param session_id [String] The session ID
    # @param validate_context [Hash] Optional validation context with :ip_address and :user_agent
    # @return [Hash, nil] The session data or nil if not found/invalid
    def find_session(user_id, session_id, validate_context: nil)
      return nil unless user_id && session_id
      
      session = RedisService.get(:jwt_sessions, [user_id, session_id])
      return nil unless session
      
      # Validate session context for security if validation params provided
      if validate_context && !validate_session_context(session, validate_context)
        Rails.logger.warn "JWT session validation failed for user #{user_id}, session #{session_id}: context mismatch"
        # Destroy compromised session for security
        destroy_session(user_id, session_id)
        return nil
      end
      
      # Update last activity timestamp
      update_last_activity(user_id, session_id)
      
      session
    rescue => e
      Rails.logger.error "Failed to find JWT session: #{e.message}"
      nil
    end
    
    # Update the access token for a session (after refresh)
    # @param user_id [Integer] The user's ID
    # @param session_id [String] The session ID
    # @param new_access_token [String] The new access token
    def update_session_token(user_id, session_id, new_access_token)
      session = find_session(user_id, session_id)
      return false unless session
      
      session[:access_token] = new_access_token
      session[:last_activity] = Time.current.to_i
      
      # Reset TTL on update using namespaced service
      RedisService.set(:jwt_sessions, [user_id, session_id], session, ttl: SESSION_TTL)
      
      true
    rescue => e
      Rails.logger.error "Failed to update JWT session token: #{e.message}"
      false
    end
    
    # Destroy a specific session
    # @param user_id [Integer] The user's ID
    # @param session_id [String] The session ID
    # SECURITY FIX TYM-55: Remove exception handling for consistency with destroy_all_user_sessions
    def destroy_session(user_id, session_id)
      return unless user_id && session_id
      
      # Remove from Redis using namespaced service
      RedisService.delete(:jwt_sessions, [user_id, session_id])
      
      # Remove from user's session list
      remove_from_user_sessions(user_id, session_id)
      
      Rails.logger.info "Destroyed JWT session for user #{user_id}: #{session_id}"
      true
    end
    
    # Destroy all sessions for a user (logout from all devices)
    # @param user_id [Integer] The user's ID
    # SECURITY FIX TYM-55: Remove exception handling to ensure transaction rollback on failure
    # If Redis/session invalidation fails, the calling transaction should fail too
    def destroy_all_user_sessions(user_id)
      session_ids = get_user_sessions(user_id)
      
      session_ids.each do |session_id|
        destroy_session(user_id, session_id)
      end
      
      # Clear the user's session list using namespaced service
      RedisService.delete(:jwt_sessions, ['user', user_id, 'list'])
      
      Rails.logger.info "Destroyed all JWT sessions for user #{user_id}"
      true
    end
    
    # Get all active sessions for a user
    # @param user_id [Integer] The user's ID
    # @return [Array<Hash>] Array of session data
    def get_user_sessions_details(user_id)
      session_ids = get_user_sessions(user_id)
      
      sessions = []
      session_ids.each do |session_id|
        session = find_session(user_id, session_id)
        if session
          sessions << session.merge(session_id: session_id)
        end
      end
      
      sessions
    end
    
    private
    
    # Generate Redis key for a session using centralized namespacing
    def session_key(user_id, session_id)
      RedisKeyBuilder.jwt_session_key(user_id, session_id)
    end
    
    # Generate Redis key for user's session list using centralized namespacing
    def user_sessions_key(user_id)
      RedisKeyBuilder.jwt_user_sessions_key(user_id)
    end
    
    # Add session to user's session list using namespaced service
    def add_to_user_sessions(user_id, session_id)
      RedisService.sadd(:jwt_sessions, ['user', user_id, 'list'], session_id)
      # Set expiry on the set to match session TTL
      RedisService.expire(:jwt_sessions, ['user', user_id, 'list'], SESSION_TTL)
    end
    
    # Remove session from user's session list using namespaced service
    def remove_from_user_sessions(user_id, session_id)
      RedisService.srem(:jwt_sessions, ['user', user_id, 'list'], session_id)
    end
    
    # Get all session IDs for a user using namespaced service
    def get_user_sessions(user_id)
      RedisService.smembers(:jwt_sessions, ['user', user_id, 'list'])
    end
    
    # Update last activity timestamp without changing other data
    def update_last_activity(user_id, session_id)
      ttl = RedisService.ttl(:jwt_sessions, [user_id, session_id])
      
      # Only update if session still has reasonable TTL
      if ttl > 3600 # More than 1 hour left
        RedisService.expire(:jwt_sessions, [user_id, session_id], SESSION_TTL)
      end
    end
    
    # Validate session context against stored values for security
    # TODO: Add configuration system for validation strictness (see docs/redis_jwt_improvements_roadmap.md #1.1)
    # TODO: Consider adding configurable tolerance for IP changes (mobile users) (see roadmap #1.3)
    # TODO: Add Redis connectivity resilience with fallback storage (see roadmap #2.1)
    def validate_session_context(session, context)
      stored_ip = session[:ip_address]
      stored_user_agent = session[:user_agent]
      current_ip = context[:ip_address]
      current_user_agent = context[:user_agent]
      
      # Check IP address match (strict for now)
      if stored_ip.present? && current_ip.present? && stored_ip != current_ip
        Rails.logger.warn "Session IP mismatch: stored=#{stored_ip}, current=#{current_ip}"
        return false
      end
      
      # Check User-Agent match (more lenient - check if core browser is same)
      if stored_user_agent.present? && current_user_agent.present?
        unless user_agent_match?(stored_user_agent, current_user_agent)
          Rails.logger.warn "Session User-Agent mismatch: stored=#{stored_user_agent}, current=#{current_user_agent}"
          return false
        end
      end
      
      true
    end
    
    # Check if user agents are compatible (allows for minor version updates)
    # TODO: Use proper User-Agent parsing library (browser gem) for better mobile compatibility (see roadmap #1.2)
    def user_agent_match?(stored, current)
      # Extract browser and major version from user agent
      stored_browser = extract_browser_signature(stored)
      current_browser = extract_browser_signature(current)
      
      stored_browser == current_browser
    end
    
    # Extract browser signature for comparison
    # TODO: Replace with proper UA parsing library once mobile compatibility is prioritized (see roadmap #1.2)
    def extract_browser_signature(user_agent)
      return nil if user_agent.blank?
      
      # Basic extraction - matches major browser and version
      case user_agent.downcase
      when /chrome\/(\d+)/
        "chrome-#{$1.to_i / 10}" # Group by major version groups
      when /firefox\/(\d+)/
        "firefox-#{$1.to_i / 10}"
      when /safari\/(\d+).*version\/(\d+)/
        "safari-#{$2.to_i}"
      when /edge\/(\d+)/
        "edge-#{$1.to_i / 10}"
      else
        # Fallback to first 50 chars for other browsers
        user_agent[0..49]
      end
    end
  end
end