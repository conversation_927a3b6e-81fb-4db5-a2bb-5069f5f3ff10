# ABOUTME: Critical security tests for cross-company authorization bypass
# ABOUTME: Tests the exact TYM-55 vulnerability - users accessing companies they don't belong to

require 'rails_helper'

RSpec.describe 'Company Settings Cross-Company Security', type: :request do
  describe 'TYM-55 CRITICAL: Cross-Company Authorization Bypass' do
    let(:company_a) { create(:company, name: "Company A") }
    let(:company_b) { create(:company, name: "Company B") } 
    let(:user) { create(:user) }
    let(:owner_role) { create(:role, name: 'owner') }
    let(:employee_role) { create(:role, name: 'employee') }

    before do
      # User is ONLY owner in Company A - has NO access to Company B
      create(:company_user_role, 
        user: user, 
        company: company_a, 
        role: owner_role, 
        active: true,
        is_primary: true
      )
      
      # Ensure user has NO role in Company B
      expect(user.company_user_roles.where(company: company_b)).to be_empty
    end

    describe 'Cross-Company Attack Scenarios' do
      context 'when user has owner role in Company A but no access to Company B' do
        it 'BLOCKS access to Company B settings via JWT manipulation' do
          # Attempt to create JWT token for Company B (where user has no access)
          jwt_payload = {
            'user_id' => user.id,
            'company_id' => company_b.id,  # ATTACK: Wrong company ID
            'exp' => 1.hour.from_now.to_i
          }
          malicious_token = JwtService.encode(jwt_payload)

          # Attack attempt: Try to edit Company B settings
          put "/company_settings", 
              params: { company_setting: { approve_vacations: true } },
              headers: { 'Authorization' => "Bearer #{malicious_token}" }

          # MUST be blocked - user has no access to Company B
          expect(response).to have_http_status(:unauthorized)
          
          # Verify Company B settings were NOT changed
          expect(company_b.company_setting.reload.approve_vacations).to be_falsy
        end

        it 'BLOCKS cross-company access even with valid JWT format' do
          # Create seemingly valid JWT but for wrong company
          jwt_payload = {
            'user_id' => user.id,
            'company_id' => company_b.id,  # User has no role in this company
            'exp' => 1.hour.from_now.to_i,
            'jti' => SecureRandom.uuid
          }
          token = JwtService.encode(jwt_payload)

          # Attempt to access Company B
          get "/api/v1/companies", 
              headers: { 'Authorization' => "Bearer #{token}" }

          # Should be rejected - no active role in Company B
          expect(response).to have_http_status(:unauthorized)
        end
      end

      context 'when user has pending/employee role in Company B' do
        before do
          # Add user as employee (not owner) in Company B
          create(:company_user_role,
            user: user,
            company: company_b, 
            role: employee_role,
            active: true,
            is_primary: false  # Not primary since user already has primary in Company A
          )
        end

        it 'BLOCKS company settings access for non-owner role' do
          # Create JWT for Company B where user is only employee
          jwt_payload = {
            'user_id' => user.id,
            'company_id' => company_b.id,
            'exp' => 1.hour.from_now.to_i
          }
          token = JwtService.encode(jwt_payload)

          # Try to edit Company B settings as employee
          put "/company_settings",
              params: { company_setting: { approve_vacations: true } },
              headers: { 'Authorization' => "Bearer #{token}" }

          # Should be forbidden - employee cannot manage company settings
          expect(response).to have_http_status(:forbidden)
          
          # Verify settings were not changed
          expect(company_b.company_setting.reload.approve_vacations).to be_falsy
        end
      end

      context 'when user has deactivated role in Company B' do
        before do
          # Create deactivated owner role in Company B
          create(:company_user_role,
            user: user,
            company: company_b,
            role: owner_role, 
            active: false,  # Deactivated
            is_primary: false  # Not primary
          )
        end

        it 'BLOCKS access for deactivated roles' do
          # Attempt to use JWT for Company B with deactivated role
          jwt_payload = {
            'user_id' => user.id,
            'company_id' => company_b.id,
            'exp' => 1.hour.from_now.to_i
          }
          token = JwtService.encode(jwt_payload)

          # Try to access Company B with deactivated role
          put "/company_settings",
              params: { company_setting: { approve_vacations: true } },
              headers: { 'Authorization' => "Bearer #{token}" }

          # Should be unauthorized - deactivated role
          expect(response).to have_http_status(:unauthorized)
        end
      end
    end

    describe 'Role Cache Isolation' do
      it 'does not leak role data between companies' do
        # Access Company A (where user is owner)
        company_a_jwt = {
          'user_id' => user.id,
          'company_id' => company_a.id,
          'exp' => 1.hour.from_now.to_i
        }
        token_a = JwtService.encode(company_a_jwt)

        # Verify access to Company A works
        get "/api/v1/companies",
            headers: { 'Authorization' => "Bearer #{token_a}" }
        expect(response).to have_http_status(:ok)

        # Now attempt Company B (where user has no role)
        company_b_jwt = {
          'user_id' => user.id, 
          'company_id' => company_b.id,
          'exp' => 1.hour.from_now.to_i
        }
        token_b = JwtService.encode(company_b_jwt)

        # Should be blocked despite previous Company A access
        get "/api/v1/companies",
            headers: { 'Authorization' => "Bearer #{token_b}" }
        expect(response).to have_http_status(:unauthorized)
      end
    end

    describe 'Security Logging' do
      it 'logs unauthorized cross-company access attempts' do
        expect(AuthHealthCheck).to receive(:log_security_event)
          .with('unauthorized_company_access', hash_including(
            severity: 'high',
            user_id: user.id,
            company_id: company_b.id,
            reason: 'no_active_role'
          ))

        # Attempt unauthorized access
        jwt_payload = {
          'user_id' => user.id,
          'company_id' => company_b.id,
          'exp' => 1.hour.from_now.to_i
        }
        token = JwtService.encode(jwt_payload)

        put "/company_settings",
            params: { company_setting: { approve_vacations: true } },
            headers: { 'Authorization' => "Bearer #{token}" }

        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
end