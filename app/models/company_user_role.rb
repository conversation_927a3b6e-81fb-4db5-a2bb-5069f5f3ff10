class CompanyUserRole < ApplicationRecord
  belongs_to :company
  belongs_to :user
  belongs_to :role
  
  validates :is_primary, uniqueness: { scope: :user_id }, if: :is_primary
  validates :company_id, uniqueness: { 
    scope: [:user_id, :role_id, :active], 
    message: "User can have only one active role of each type in a company" 
  }

  # Only show active roles by default
  default_scope { where(active: true) }

  # Check this version as the scope have been changed when creating tests
  # Define scopes to access all roles when needed
  scope :with_inactive, -> { unscope(where: :active) }
  scope :active, -> { where(active: true) }
  scope :inactive, -> { where(active: false) }

  # SECURITY FIX: TYM-55 - Invalidate JWT sessions when roles change
  # This prevents users from retaining access with stale JWT tokens after role changes
  after_update :invalidate_user_sessions_if_needed
  after_destroy :invalidate_user_sessions_on_removal

  # TODO: rework to enums
  # For this model is a con of enums: 
  # More rigid if roles need to change frequently or if you need to add complex logic related to roles (e.g., a Role can have many permissions).
  # enum role: { admin: 0, manager: 1, employee: 2 }  # Example roles

  private

  # SECURITY: Check if session invalidation is needed and trigger it
  def invalidate_user_sessions_if_needed
    role_deactivated = saved_change_to_active? && !active?
    role_id_changed = saved_change_to_role_id?

    if role_deactivated || role_id_changed
      changes = []
      changes << "deactivated" if role_deactivated
      changes << "changed" if role_id_changed
      reason = "Role #{changes.join(' and ')}"
      
      trigger_session_invalidation(reason)
    end
  end

  # SECURITY: Invalidate all JWT sessions when user role is completely removed
  def invalidate_user_sessions_on_removal
    trigger_session_invalidation("Role removed")
  end

  # SECURITY: Common method to invalidate sessions and clear caches
  def trigger_session_invalidation(reason)
    Rails.logger.info "TYM-55 Security: #{reason} for user #{user_id} in company #{company_id}, invalidating JWT sessions"
    
    # CRITICAL: If session invalidation fails, this will raise an exception
    # causing the database transaction to rollback, maintaining data consistency
    JwtSessionService.destroy_all_user_sessions(user_id)
    
    # Clear user's authorization cache
    user.invalidate_role_cache! if user
  end
end
